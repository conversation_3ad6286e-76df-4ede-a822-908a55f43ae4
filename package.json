{"name": "relationship-chatbot", "private": true, "version": "1.0.0", "author": "liuping03", "license": "ISC", "scripts": {"dev": "cross-env NODE_ENV=dev node  webpack/dev.config.js", "build": "cross-env NODE_ENV=production node  webpack/production.config.js", "build:test": "cross-env NODE_ENV=production node  webpack/test.config.js", "lint": "echo \"no lint\"", "lint:fix": "eslint --ext .js,.ts -f ./node_modules/eslint-friendly-formatter --fix src", "format": "prettier --write \"src/**/*.{js,ts,vue,scss,css,json}\"", "fix-all": "npm run format && npm run lint:fix"}, "engines": {"node": "=16.14.0"}, "dependencies": {"@ai/mss-upload-js": "^1.1.8", "@cs/multi-interaction-fe": "^1.0.7", "@microsoft/fetch-event-source": "^2.0.1", "@mtfe/sso-web": "^2.4.1", "@types/d3": "^7.4.3", "@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^10.2.1", "3d-force-graph": "^1.77.0", "axios": "^1.6.8", "d3": "^7.9.0", "dompurify": "^3.0.1", "echarts": "^5.4.3", "github-markdown-css": "^5.2.0", "highlight.js": "^11.8.0", "lodash-es": "^4.17.21", "markdown-it": "^13.0.2", "markdown-it-footnote": "^3.0.3", "markdown-it-link-attributes": "^4.0.1", "marked": "^4.3.0", "path-browserify": "^1.0.1", "pinia": "^2.0.33", "qrcode": "^1.5.4", "qs": "^6.11.2", "recorder-realtime": "^1.3.0", "recordrtc": "^5.6.2", "swiper": "^11.2.8", "three-spritetext": "^1.9.6", "vant": "^4.9.16", "vue": "^3.2.45", "vue-router": "4"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/preset-env": "^7.21.4", "@babel/preset-typescript": "^7.21.4", "@babel/runtime": "^7.21.0", "@cs/eslint-config": "^2.0.32", "@cs/eslint-plugin": "^1.0.4", "@soda/friendly-errors-webpack-plugin": "^1.8.1", "@types/dompurify": "^2.4.0", "@types/lodash-es": "^4.17.10", "@types/markdown-it": "^14.1.2", "@types/markdown-it-footnote": "^3.0.4", "@types/markdown-it-link-attributes": "^3.0.5", "@types/marked": "^4.0.8", "@types/node": "^18.16.2", "@types/qrcode": "^1.5.5", "@types/recordrtc": "^5.6.14", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "@vue/compiler-core": "^3.2.47", "babel-loader": "^9.1.2", "babel-plugin-import": "^1.13.6", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "cssnano": "^6.0.0", "eslint": "^8.52.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^6.15.0", "eslint-friendly-formatter": "^4.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.18.0", "eslint-webpack-plugin": "^4.0.1", "html-webpack-plugin": "^5.5.1", "mini-css-extract-plugin": "^2.7.5", "ora": "^5.1.0", "postcss": "^8.5.2", "postcss-loader": "^7.2.4", "postcss-preset-env": "^8.3.2", "postcss-pxtorem": "^6.1.0", "prettier": "^3.0.3", "process": "^0.11.10", "sass": "^1.62.0", "sass-loader": "^13.2.2", "style-loader": "^3.3.2", "svg-sprite-loader": "^6.0.11", "terser-webpack-plugin": "^5.3.7", "typescript": "^4.9.3", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.24.1", "vue-loader": "^17.0.1", "vue-style-loader": "^4.1.3", "webpack": "^5.80.0", "webpack-bundle-analyzer": "^4.8.0", "webpack-dev-server": "^4.13.3", "webpack-merge": "^5.8.0"}, "browser": {"path": false}}