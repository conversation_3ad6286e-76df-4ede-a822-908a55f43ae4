export class Typewriter {
  private queue: string[] = [];

  private consuming = false;

  private timer: ReturnType<typeof setTimeout>;

  private lastContent = ''; // 记录上次的完整内容

  private displayedContent = ''; // 当前已显示的内容

  private isFinished = false; // 标记是否已完成

  private onComplete?: () => void; // 完成回调

  constructor(
    private onConsume: (str: string) => void,
    onComplete?: () => void,
  ) {
    this.onComplete = onComplete;
  }

  // 公共getter方法用于调试
  get queueLength() {
    return this.queue.length;
  }

  get currentDisplayedContent() {
    return this.displayedContent;
  }

  // 优化的动态速度：更快的打字效果，减少卡顿感
  dynamicSpeed() {
    // 如果已标记完成，使用10ms速度来消费剩余队列
    if (this.isFinished) {
      return 10; // 完成后使用更快速度
    }

    // 基础速度：15ms（更快的基础速度）
    const baseSpeed = 15;

    // 如果队列为空，使用基础速度
    if (this.queue.length === 0) {
      return baseSpeed;
    }

    // 如果队列积压较多，使用更快的速度
    if (this.queue.length > 5) {
      return 8; // 队列积压时使用更快速度
    }

    // 如果队列积压严重，使用最快速度
    if (this.queue.length > 15) {
      return 5; // 严重积压时使用最快速度
    }

    return baseSpeed;
  }

  // 添加字符串到队列（处理累积文本）
  add(str: string) {
    if (!str) return;

    // 如果新内容和上次内容相同，跳过
    if (str === this.lastContent) {
      return;
    }

    // 如果新内容是上次内容的扩展，只添加增量部分
    if (str.startsWith(this.lastContent)) {
      const incrementalText = str.substring(this.lastContent.length);
      if (incrementalText) {
        // 将增量文本按字符分割，以便更流畅的显示
        const chars = incrementalText.split('');
        this.queue.push(...chars);
      }
    } else if (str.startsWith(this.displayedContent)) {
      // 如果不是扩展，检查是否是服务器重新发送了完整内容
      // 如果新内容包含了当前已显示的内容，则只添加新增部分
      const incrementalText = str.substring(this.displayedContent.length);
      if (incrementalText) {
        // 清空队列，添加从当前显示位置开始的新内容（按字符分割）
        const chars = incrementalText.split('');
        this.queue = chars;
      }
    } else {
      // 完全不同的内容，重新开始（这种情况应该很少见）
      const chars = str.split('');
      this.queue = chars;
      this.displayedContent = '';
    }

    this.lastContent = str;
  }

  // 消费
  consume() {
    if (this.queue.length > 0) {
      const incrementalText = this.queue.shift();
      if (incrementalText) {
        // 累积显示的内容
        this.displayedContent += incrementalText;
        console.log(
          '🖥️ [打字机显示]',
          `显示: "${incrementalText}" | 已显示长度: ${this.displayedContent.length} | 队列剩余: ${this.queue.length}`,
        );
        this.onConsume(this.displayedContent);
      }
    } else if (this.isFinished && this.consuming) {
      // 队列消费完毕且已标记完成，自动结束
      this.consuming = false;
      clearTimeout(this.timer);
      console.log('✅ [打字机完成] 所有内容已显示完毕');
      if (this.onComplete) {
        this.onComplete();
      }
    }
  }

  // 持续消费
  loopConsume() {
    this.consume();
    // 根据队列中字符的数量来设置消耗每一帧的速度，用定时器消耗
    this.timer = setTimeout(() => {
      if (this.consuming) {
        this.loopConsume();
      }
    }, this.dynamicSpeed());
  }

  // 开始消费队列
  start() {
    this.displayedContent = ''; // 重置已显示内容
    this.consuming = true;
    this.loopConsume();
  }

  // 结束消费队列
  done() {
    this.consuming = false;
    clearTimeout(this.timer);

    // 如果还有剩余队列，显示最终的完整内容
    if (this.queue.length > 0 || this.lastContent) {
      this.onConsume(this.lastContent);
      this.queue = [];
    }

    // 重置状态
    this.lastContent = '';
    this.displayedContent = '';
  }

  // 标记完成（不立即结束，等队列消费完毕）
  markFinished() {
    this.isFinished = true;

    // 如果队列为空且正在消费，立即完成
    if (this.queue.length === 0 && this.consuming) {
      this.consuming = false;
      clearTimeout(this.timer);
      if (this.onComplete) {
        this.onComplete();
      }
    }
  }

  // 停止打印
  stop() {
    this.consuming = false;
    this.queue = [];
    this.lastContent = '';
    this.displayedContent = '';
    this.isFinished = false;
  }
}
