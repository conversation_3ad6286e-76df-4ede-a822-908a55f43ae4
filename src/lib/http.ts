/**
 * HTTP Agent
 */
import { AxiosRequestHeaders } from 'axios';
import router from '@/router';
import instance from './axios';
import ssoWeb from './sso';
// Add request interceptor
instance.interceptors.request.use(
  // Do something before request is sent
  config => {
    config.headers = {
      ...config.headers,
      // 后端 MDP 框架接口鉴权要求有此 header
      'x-requested-with': 'XMLHttpRequest',
    } as unknown as AxiosRequestHeaders;
    return config;
  },
  // Do something with request error
  error => {
    return Promise.reject(error);
  },
);

// Add response interceptor
instance.interceptors.response.use(
  // Do something with response data
  response => {
    if (response.status === 200 && response?.config?.responseType === 'blob') {
      return response;
    }
    window.owl('addApi', {
      name: response.request.url,
      networkCode: response.status,
      statusCode: response.data.code || response.data.status,
    });
    if (response.data?.code === 451) {
      const tenantData = {
        href: window.location.href,
        pathname: window.location.pathname,
        data: response.data?.data,
      };
      localStorage.setItem('updateTenant', JSON.stringify(tenantData));
      // router.replace('/noCurrentTenant');
      router
        .replace('/noCurrentTenant')
        .then(() => {})
        .catch(error => {
          console.error(error);
        });
      return Promise.reject(response.data?.message);
    }
    // 处理传统的包装响应格式 (status/code = 0 表示成功)
    if (response.data?.status === 0 || response.data?.code === 0) {
      return response.data.data;
    }

    // 处理新的直接业务数据格式 (success = true 表示成功)
    if (response.data?.success === true) {
      return response.data;
    }

    // 处理 status = "success" 格式 (特别针对 /humanrelation/history 等接口)
    if (response.data?.status === 'success') {
      return response.data;
    }

    if (response.data.status === 401 || response.data?.code === 401) {
      window.location.href = ssoWeb.getLoginUrl();
    }
    if (response.headers['content-type'].includes('stream')) {
      const data = {
        file: response.headers,
        data: response.data,
      };
      return data;
    }
    if (response.data.code === 1001) {
      return Promise.reject(response.data);
    }

    return Promise.reject(response.data?.message || response.data?.msg);
  },
  // Do something with response error
  error => {
    if (error.response && error.response.status === 401) {
      window.location.href = ssoWeb.getLoginUrl();
    }
    return Promise.reject(error);
  },
);

export default instance;
