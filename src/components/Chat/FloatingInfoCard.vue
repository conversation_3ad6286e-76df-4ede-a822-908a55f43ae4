<template>
  <div v-if="visible" class="floating-info-card">
    <!-- 关闭按钮 (仅天气卡片使用) -->
    <div v-if="cardType === 'weather'" class="close-btn" @click="handleClose">
      <svg
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18 6L6 18M6 6L18 18"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>

    <!-- 天气信息卡片 -->
    <div v-if="cardType === 'weather'" class="card-content weather-card">
      <div class="card-header">
        <div class="card-icon weather-icon">
          <svg
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6 19C4.9 19 4 18.1 4 17C4 16.1 4.4 15.4 5 15.1C5 14.5 5.2 13.9 5.6 13.4C6.1 12.8 6.8 12.5 7.5 12.5C8.2 12.5 8.9 12.8 9.4 13.4C9.8 13.9 10 14.5 10 15.1C10.6 15.4 11 16.1 11 17C11 18.1 10.1 19 9 19H6Z"
              fill="currentColor"
            />
            <path
              d="M12 2L13.09 8.26L19 7L14.74 11.26L21 12L14.74 12.74L19 17L13.09 15.74L12 22L10.91 15.74L5 17L9.26 12.74L3 12L9.26 11.26L5 7L10.91 8.26L12 2Z"
              fill="currentColor"
            />
          </svg>
        </div>
        <div class="card-title-section">
          <h3 class="card-title">天气预报解读</h3>
          <p class="card-subtitle">📍 AI智能分析</p>
        </div>
      </div>

      <div class="weather-content">
        <!-- 加载状态 -->
        <div v-if="loadingWeather" class="loading-state">
          <div class="loading-spinner"></div>
          <span class="loading-text">正在获取天气信息...</span>
        </div>

        <!-- AI提醒内容 -->
        <div v-else-if="weatherApiData && weatherApiData.ai_reminder" class="ai-reminder-content">
          <div class="ai-reminder-text">{{ weatherApiData.ai_reminder }}</div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="weatherError" class="error-state">
          <div class="error-icon">⚠️</div>
          <div class="error-text">{{ weatherError }}</div>
        </div>

        <!-- 默认状态 -->
        <div v-else class="default-state">
          <div class="default-text">暂无天气信息</div>
        </div>
      </div>
    </div>

    <!-- 记录您的情况卡片 -->
    <div v-else-if="cardType === 'record'" class="card-content record-card">
      <div class="record-row">
        <div class="card-icon record-icon">
          <svg
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14 2V8H20"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M16 13H8"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M16 17H8"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M10 9H9H8"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <p class="record-text">跟老董聊天让我更懂你吧</p>
        <div class="record-close-btn" @click="handleClose">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M18 6L6 18M6 6L18 18"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, type Ref } from 'vue';
import { getComprehensiveWeather, type IComprehensiveWeatherResponse } from '@/apis/memory';

// 定义props
interface IProps {
  visible: boolean;
  cardType: 'weather' | 'record' | null;
  userId?: string;
}

const props = defineProps<IProps>();

// 定义emits
const emit = defineEmits<{
  close: [];
}>();

// 天气API相关状态
const loadingWeather = ref(false);
const weatherApiData: Ref<IComprehensiveWeatherResponse | null> = ref(null);
const weatherError = ref<string>('');

// 定时器相关
let weatherUpdateTimer: NodeJS.Timeout | null = null;

// 获取天气数据
const loadWeatherData = async () => {
  if (!props.userId) {
    weatherError.value = '缺少用户信息，无法获取天气数据';
    return;
  }

  try {
    loadingWeather.value = true;
    weatherError.value = '';

    console.log('🔄 [FloatingInfoCard.vue] 开始获取综合天气数据...', {
      userId: props.userId,
    });

    const response = await getComprehensiveWeather({
      user_id: props.userId,
    });

    console.log('📡 [FloatingInfoCard.vue] 综合天气数据响应:', response);

    if (response && response.result === 'success') {
      weatherApiData.value = response;
      console.log('✅ [FloatingInfoCard.vue] 综合天气数据加载成功');
    } else {
      weatherError.value = '天气数据获取失败';
      console.warn('⚠️ [FloatingInfoCard.vue] 综合天气数据格式异常:', response);
    }
  } catch (error) {
    console.error('❌ [FloatingInfoCard.vue] 获取综合天气数据失败:', error);
    weatherError.value = '网络请求失败，请稍后重试';
  } finally {
    loadingWeather.value = false;
  }
};

// 启动天气数据定时更新
const startWeatherTimer = () => {
  // 清除现有定时器
  clearWeatherTimer();

  // 立即加载一次数据
  void loadWeatherData();

  // 设置60秒定时器
  weatherUpdateTimer = setInterval(() => {
    void loadWeatherData();
  }, 60000); // 60秒 = 60000毫秒

  console.log('🔄 [FloatingInfoCard.vue] 天气数据定时器已启动，每60秒更新一次');
};

// 清除天气数据定时器
const clearWeatherTimer = () => {
  if (weatherUpdateTimer) {
    clearInterval(weatherUpdateTimer);
    weatherUpdateTimer = null;
    console.log('⏹️ [FloatingInfoCard.vue] 天气数据定时器已清除');
  }
};

// 监听visible和cardType变化，当天气卡片显示时启动定时器
watch(
  () => [props.visible, props.cardType, props.userId],
  ([visible, cardType, userId]) => {
    if (visible && cardType === 'weather' && userId) {
      startWeatherTimer();
    } else {
      clearWeatherTimer();
    }
  },
  { immediate: true },
);

// 处理关闭
const handleClose = () => {
  emit('close');
};

// 组件卸载时清除定时器
onUnmounted(() => {
  clearWeatherTimer();
});
</script>

<style lang="scss" scoped>
.floating-info-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  margin: 16px 32px;
  position: relative;
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  border: 2px solid var(--border-accent);
  animation: slideInDown 0.3s ease-out;
  z-index: 10;
  max-width: 640px;
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-tertiary);
  transition: all 0.3s ease;
  border: 1px solid var(--border-glass);

  &:hover {
    background: var(--primary-color-medium);
    color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: var(--shadow-accent);
  }
}

.card-content {
  width: 100%;
}

.card-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 16px;
}

.card-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color-light);
  border-radius: 12px;
  border: 2px solid var(--border-accent);
  color: var(--accent-color);
  flex-shrink: 0;
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-color-medium);
    transform: translateY(-2px);
    box-shadow: var(--shadow-accent);
  }
}

.card-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.card-subtitle {
  font-size: var(--font-size-xl);
  font-weight: 400;
  color: var(--text-tertiary);
  margin: 0;
  line-height: 1.3;
}

.weather-content {
  width: 100%;
}

// 加载状态样式
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 24px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 28px;
  height: 28px;
  border: 2px solid var(--border-glass);
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 22px;
}

// AI提醒内容样式
.ai-reminder-content {
  height: 250px;
  padding: 20px;
  background: var(--primary-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-glass);
  border-left: 3px solid var(--accent-color);
  overflow-y: auto;
}

.ai-reminder-text {
  font-size: calc(var(--font-size-lg) + 4px);
  line-height: 1.6;
  color: var(--text-primary);
  white-space: pre-wrap;
}

// 错误状态样式
.error-state {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.error-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.error-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

// 默认状态样式
.default-state {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary);
}

.default-text {
  font-size: var(--font-size-base);
}

.weather-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 12px;
}

.weather-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--primary-color-light);
  border-radius: 12px;
  border: 1px solid var(--border-glass);
  border-left: 3px solid var(--accent-color);
  transition: all 0.3s ease;
  gap: 12px;

  &:hover {
    background: var(--primary-color-medium);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }
}

.weather-item-icon {
  font-size: var(--font-size-2xl);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.weather-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.label {
  font-size: var(--font-size-2xl);
  color: var(--text-tertiary);
  font-weight: 500;
  line-height: 1.2;
}

.value {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.2;
}

.record-row {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.record-text {
  flex: 1;
  font-size: var(--font-size-3xl);
  color: var(--text-primary);
  font-weight: 500;
  margin: 0;
  line-height: 1.5;
  text-align: center;
}

.record-close-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-tertiary);
  transition: all 0.3s ease;
  border: 1px solid var(--border-glass);
  flex-shrink: 0;

  &:hover {
    background: var(--primary-color-medium);
    color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: var(--shadow-accent);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
