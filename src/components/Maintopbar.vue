<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <button v-if="showBackBtn" class="back-btn" style="margin-right: 5px" @click="$emit('back')">
        <img src="@/assets/icon/back.png" alt="返回" />
      </button>
      <div
        v-if="showHistoryBtn"
        class="history-btn-container"
        style="margin-right: 5px"
        @click.stop="$emit('history')"
      >
        <div class="history-btn">
          <img src="@/assets/img/history.png" alt="历史" />
        </div>
        <div class="history-label">历史会话</div>
      </div>
      <div
        v-if="showUserAvatar && !userLoading"
        class="user-avatar"
        :style="{ backgroundColor: getRandomColor(currentMisId) }"
        @click="$emit('avatar-click')"
      >
        {{ getAvatarLetter(currentMisId) }}
      </div>
      <div v-else-if="showUserAvatar && userLoading" class="user-avatar loading-avatar">
        <span class="loading-dot"></span>
      </div>
      <div
        v-if="showPersonListBtn"
        class="person-list-btn-container"
        @click.stop="$emit('person-list')"
      >
        <div class="person-list-btn">
          <img src="@/assets/icon/contactsPersonList.png" alt="人员列表" />
        </div>
        <div class="person-list-label">亲友名单</div>
      </div>
      <div
        v-if="showRelationshipBtn"
        class="relationship-btn-container"
        @click.stop="$emit('relationship')"
      >
        <div class="relationship-btn-left">
          <img src="@/assets/icon/branch.png" alt="关系拓扑" />
        </div>
        <div class="relationship-label">关系图</div>
      </div>
      <div v-if="showUserAvatar" class="placeholder"></div>
    </div>
    <!-- 后续需要添加中间区域在这里修改 -->
    <!-- 右侧按钮 -->
    <div class="header-right">
      <span v-if="showVoiceBtn" class="chat-voice" @click="$emit('voice')">
        <img v-if="isChatPlay" class="kaiqi-img" src="@/assets/img/langdu_kaiqi.png" alt="" />
        <i v-else class="iconfont icon-roo-sqt-laba"></i>
      </span>

      <div v-if="showAddChatBtn" class="add-chat" @click.stop="$emit('add-chat')">
        <img v-if="addChatType === 'add'" src="@/assets/img/add-message.png" alt="新建" />
        <img v-else src="@/assets/img/search.svg" alt="搜索" />
      </div>
      <div v-if="showWeatherBtn" class="weather-btn-container" @click.stop="$emit('weather')">
        <div class="weather-btn">
          <img src="@/assets/icon/weather.png" alt="天气" />
        </div>
        <div class="weather-label">天气</div>
      </div>
      <div v-if="showMemoBtn" class="memo-btn-container" @click.stop="$emit('memo')">
        <div class="memo-btn">
          <img src="@/assets/icon/memo.png" alt="备忘录" />
        </div>
        <div class="memo-label">备忘录</div>
      </div>
      <div v-if="showHomeBtn" class="home-btn-container" @click.stop="$emit('home')">
        <div class="home-btn">
          <img
            :src="selectedAssistantAvatar || '@/assets/icon/assistant_avatar.png'"
            alt="AI助理"
          />
        </div>
        <div class="home-label">首页</div>
      </div>
    </div>
    <div v-if="showHeaderGrad" class="header-grad"></div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

defineProps({
  showBackBtn: Boolean,
  showHistoryBtn: Boolean,
  showUserAvatar: Boolean,
  showAssistantAvatar: Boolean,
  assistantAvatarSrc: {
    type: String,
    default: '@/assets/icon/assistant_avatar.png',
  },
  assistantName: {
    type: String,
    default: '备忘录',
  },
  selectedAssistantAvatar: {
    type: String,
    default: '@/assets/icon/assistant_avatar.png',
  },
  showVoiceBtn: Boolean,
  showPersonListBtn: Boolean,
  showRelationshipBtn: Boolean,
  showAddChatBtn: Boolean,
  showHomeBtn: Boolean,
  showWeatherBtn: Boolean,
  showMemoBtn: Boolean,
  addChatType: {
    type: String,
    default: 'add', // "add" | "search"
  },
  isChatPlay: Boolean,
  userLoading: Boolean,
  currentMisId: {
    type: String,
    default: '',
  },
  getRandomColor: {
    type: Function,
    default: () => '#ccc',
  },
  getAvatarLetter: {
    type: Function,
    default: () => '',
  },
  showHeaderGrad: Boolean,
});

defineEmits([
  'back',
  'history',
  'voice',
  'person-list',
  'relationship',
  'add-chat',
  'home',
  'avatar-click',
  'memo',
  'weather',
]);
</script>

<style scoped lang="scss">
.header {
  height: 125px;
  padding: 10px 40px 40px;
  margin-bottom: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 72px; // 增大按钮尺寸
      height: 72px;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }

      img {
        width: 40px;
        height: 40px;
        filter: brightness(0) invert(1); // 将图标变为白色
      }
    }

    .history-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .history-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;

        img {
          width: 40px;
          height: 40px;
          filter: brightness(0) invert(1); // 将图标变为白色
        }
      }

      .history-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--text-primary);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .history-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .history-btn {
          transform: translateY(0);
        }
      }
    }
    .relationship-btn {
      width: 68px;
      height: 68px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }

      img {
        width: 40px;
        height: 40px;
        filter: brightness(0) invert(1); // 将图标变为白色
      }
    }

    .user-avatar {
      width: 72px; // 增大头像尺寸
      height: 72px;
      border-radius: 50%;
      color: var(--text-primary);
      font-size: 32px;
      font-weight: bold;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      background: var(--primary-color-light);
      border: 2px solid var(--primary-color);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-color-medium);
        transform: translateY(-2px);
      }

      &.loading-avatar {
        background: var(--bg-glass);
        border-color: var(--border-glass);

        .loading-dot {
          display: inline-block;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: var(--accent-color);
          animation: avatar-loading 1s infinite alternate;
        }
      }
    }

    .assistant-avatar-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;

      .assistant-label {
        font-size: 24px;
        color: var(--text-primary);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }
    }

    .relationship-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .relationship-btn-left {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;

        img {
          width: 40px;
          height: 40px;
          filter: brightness(0) invert(1); // 将图标变为白色
        }
      }

      .relationship-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--text-primary);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .relationship-btn-left {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .relationship-btn-left {
          transform: translateY(0);
        }
      }
    }
    .placeholder {
      width: 16px;
      height: 16px;
      visibility: hidden;
    }
  }

  // 通用的按钮容器样式，适用于左侧和右侧区域
  .person-list-btn-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    .person-list-btn {
      width: 72px; // 增大按钮尺寸
      height: 72px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;

      img {
        width: 40px;
        height: 40px;
        filter: brightness(0) invert(1); // 将图标变为白色
      }
    }

    .person-list-label {
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 4px;
      font-size: 24px;
      color: var(--text-primary);
      font-weight: 500;
      white-space: nowrap;
      pointer-events: none; // 防止文字影响点击
    }

    &:hover {
      .person-list-btn {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }
    }

    &:active {
      .person-list-btn {
        transform: translateY(0);
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 32px;

    .chat-voice {
      width: 96px !important; // 增大按钮尺寸，使用!important确保不被rem转换影响
      height: 96px !important;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }

      .iconfont {
        font-size: 40px;
        color: var(--text-primary);
      }

      .kaiqi-img {
        width: 40px;
        height: auto;
        filter: brightness(0) invert(1);
      }
    }

    .add-chat {
      width: 72px; // 增大按钮尺寸
      height: 72px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }

      img {
        width: 40px;
        height: 40px;
        filter: brightness(0) invert(1); // 将图标变为白色
      }
    }

    .weather-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .weather-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;

        img {
          width: 40px;
          height: 40px;
          filter: brightness(0) invert(1); // 将图标变为白色
        }
      }

      .weather-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--text-primary);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .weather-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .weather-btn {
          transform: translateY(0);
        }
      }
    }

    .memo-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .memo-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;

        img {
          width: 40px;
          height: 40px;
          filter: brightness(0) invert(1); // 将图标变为白色
        }
      }

      .memo-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--text-primary);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .memo-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .memo-btn {
          transform: translateY(0);
        }
      }
    }

    .home-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .home-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;

        img {
          width: 70px;
          height: 70px;
          border-radius: 50%; // AI助理头像需要圆形显示
        }
      }

      .home-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--text-primary);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .home-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .home-btn {
          transform: translateY(0);
        }
      }
    }
  }

  .header-grad {
    position: absolute;
    top: 128px;
    left: 0px;
    width: 100%;
    height: 100px;
    opacity: 1;
    background: linear-gradient(180deg, var(--primary-color-light) 0%, rgba(0, 188, 212, 0) 100%);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .header {
    padding: 15px 20px;

    .header-left,
    .header-right {
      gap: 12px;

      .back-btn,
      .history-btn-container .history-btn,
      .relationship-btn,
      .user-avatar,
      .chat-voice,
      .person-list-btn-container .person-list-btn,
      .relationship-btn-container .relationship-btn-left,
      .add-chat,
      .memo-btn-container .memo-btn,
      .home-btn-container .home-btn {
        width: 72px;
        height: 72px;
      }

      .assistant-avatar-container .avatar {
        width: 80px;
        height: 80px;

        img {
          width: 32px;
          height: 32px;
        }
      }

      .history-btn-container .history-label,
      .person-list-btn-container .person-list-label,
      .relationship-btn-container .relationship-label,
      .weather-btn-container .weather-label,
      .memo-btn-container .memo-label,
      .home-btn-container .home-label,
      .assistant-avatar-container .assistant-label {
        font-size: 20px;
        margin-top: 2px;
      }

      .user-avatar {
        font-size: 24px;
      }

      .chat-voice .iconfont {
        font-size: 32px;
      }
    }
  }
}
@keyframes avatar-loading {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
</style>
