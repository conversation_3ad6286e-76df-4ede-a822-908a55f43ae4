<template>
  <div class="relation-graph-container">
    <div class="relation-graph-page">
      <!-- 头部区域 -->
      <div class="header">
        <Maintopbar
          :show-back-btn="false"
          :show-history-btn="false"
          :show-relationship-btn="false"
          :show-user-avatar="false"
          :show-assistant-avatar="true"
          :assistant-avatar-src="selectedAssistantAvatar"
          :assistant-name="selectedAssistantName"
          :selected-assistant-avatar="selectedAssistantAvatar"
          :show-voice-btn="false"
          :show-person-list-btn="true"
          :show-add-chat-btn="false"
          :show-home-btn="true"
          add-chat-type="add"
          :is-chat-play="isChatPlay"
          :user-loading="userLoading"
          :current-mis-id="currentMisId"
          :get-random-color="getRandomColor"
          :get-avatar-letter="getAvatarLetter"
          :show-header-grad="false"
          @history="toggleHistorySidebar"
          @voice="handleChatPlay"
          @person-list="handlePersonListClick"
          @relationship="handleRelationship"
          @add-chat="handleAddChat"
          @avatar-click="handleAvatarClick"
          @home="handleHome"
        />
      </div>

      <!-- EChart关系图区域 -->
      <div class="graph-container">
        <div v-if="loading" class="loading-container">
          <div class="loading-text">正在加载关系图数据...</div>
        </div>
        <div v-else-if="echartGraphData" class="graph-content">
          <!-- 当只有核心节点时显示欢迎界面 -->
          <div v-if="isOnlyCoreNode" class="welcome-container">
            <div class="welcome-content">
              <div class="welcome-header">
                <div class="welcome-icon">
                  <img
                    :src="selectedAssistantAvatar"
                    :alt="selectedAssistantName + '助手'"
                    class="assistant-avatar"
                  />
                </div>
                <div class="cyber-title">欢迎来到人际关系助手</div>
                <div class="cyber-subtitle">
                  我是{{ selectedAssistantName }}，你的专属人际关系管理助手
                </div>
              </div>

              <div class="welcome-features">
                <div class="cyber-feature-card">
                  <div class="cyber-feature-icon">💬</div>
                  <div class="feature-text">
                    <div class="cyber-feature-title">智能对话</div>
                    <div class="cyber-feature-desc">与我聊天，分享你身边的人和事</div>
                  </div>
                </div>
                <div class="cyber-feature-card">
                  <div class="cyber-feature-icon">🕸️</div>
                  <div class="feature-text">
                    <div class="cyber-feature-title">关系网络</div>
                    <div class="cyber-feature-desc">自动构建你的人际关系图谱</div>
                  </div>
                </div>
                <div class="cyber-feature-card">
                  <div class="cyber-feature-icon">📝</div>
                  <div class="feature-text">
                    <div class="cyber-feature-title">事件记录</div>
                    <div class="cyber-feature-desc">记住重要的人际互动和事件</div>
                  </div>
                </div>
              </div>

              <div class="welcome-actions">
                <button class="cyber-btn-primary" @click="handleStartChat">
                  <span class="btn-icon">💬</span>
                  开始聊天
                </button>
                <button class="cyber-btn-secondary" @click="handleCoreNodeClick">
                  <span class="btn-icon">👥</span>
                  管理联系人
                </button>
              </div>
            </div>
          </div>
          <!-- 当有多个节点时显示EChart关系图 -->
          <EchartRelationGraph
            v-else
            :data="echartGraphData"
            :width="'100%'"
            :height="'100%'"
            @node-click="handleNodeClick"
          />
        </div>
        <div v-else class="error-container">
          <div class="error-text">暂无关系图数据</div>
        </div>
      </div>

      <!-- 底部事件容器 - 空状态下隐藏 -->
      <div v-if="!isOnlyCoreNode || events.length > 0" class="event-container">
        <!-- 有事件时显示事件列表 -->
        <div v-if="events.length > 0" class="event-swiper-container">
          <swiper
            :slides-per-view="'auto'"
            :space-between="20"
            :centered-slides="false"
            :free-mode="true"
            :grab-cursor="true"
            class="event-swiper"
          >
            <swiper-slide v-for="event in events" :key="event.event_id" class="event-slide">
              <EventItem :event-data="event" @click="handleEventItemClick(event)" />
            </swiper-slide>
            <!-- 添加事件卡片 -->
            <swiper-slide class="event-slide">
              <AddEventCard @click="handleAddEventClick" />
            </swiper-slide>
          </swiper>
        </div>

        <!-- 没有事件时显示添加卡片 -->
        <div v-else-if="!loading && events.length === 0" class="empty-events-container">
          <div class="empty-events-content">
            <div class="empty-add-card">
              <AddEventCard @click="handleAddEventClick" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 放大的事件卡片遮罩层 - 独立于主容器 -->
    <div v-if="showExpandedCard" class="expanded-card-overlay">
      <div class="expanded-card-container">
        <div class="expanded-card">
          <div class="expanded-card-header">
            <div class="expanded-event-time">
              {{ formatTime(selectedEvent?.timestamp || '') }}
            </div>
            <div
              class="expanded-event-sentiment"
              :class="getExpandedSentimentClass(selectedEvent?.sentiment || '')"
            >
              {{ getSentimentDisplayText(selectedEvent?.sentiment || '') }}
            </div>
            <button class="close-btn" @click="handleCloseExpandedCard">
              <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
            </button>
          </div>

          <div class="expanded-card-content">
            <h2 class="expanded-event-description">
              {{ selectedEvent?.description_text }}
            </h2>

            <div class="expanded-event-details">
              <div
                v-if="selectedEvent?.participants && selectedEvent.participants.length > 0"
                class="expanded-detail-item"
              >
                <span class="expanded-detail-label">参与者:</span>
                <span
                  v-for="participant in getDisplayParticipants(selectedEvent.participants)"
                  :key="participant"
                  class="expanded-detail-value tag-style"
                >
                  {{ participant }}
                </span>
              </div>

              <div
                v-if="selectedEvent?.topics && selectedEvent.topics.length > 0"
                class="expanded-detail-item"
              >
                <span class="expanded-detail-label">话题:</span>
                <span
                  v-for="topic in selectedEvent.topics"
                  :key="topic"
                  class="expanded-detail-value tag-style"
                >
                  {{ topic }}
                </span>
              </div>

              <div v-if="selectedEvent?.location" class="expanded-detail-item">
                <span class="expanded-detail-label">地点:</span>
                <span class="expanded-detail-value tag-style">{{ selectedEvent.location }}</span>
              </div>
            </div>

            <!-- 底部按钮区域 -->
            <div class="expanded-card-actions">
              <button class="action-btn edit-btn" @click="handleEditEvent">修改事件</button>
              <button class="action-btn delete-btn" @click="handleDeleteEvent">删除事件</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 人员详情弹窗 -->
    <PersonDetailPopup
      v-if="showPersonDetailPopup"
      :user-id="currentMisId"
      :person-id="selectedPersonId"
      :person-name="selectedPersonName"
      :is-user-profile="selectedIsUserProfile"
      @close="closePersonDetailPopup"
      @refresh="handleRefreshRelationGraph"
    />

    <!-- 事件编辑弹窗 -->
    <EventEditPopup
      :show="showEventEditPopup"
      :event-data="editingEvent"
      :persons-data="personsData"
      @close="handleCloseEventEditPopup"
      @save="handleSaveEventEdit"
      @update="handleUpdateEventEdit"
    />

    <!-- 事件添加弹窗 -->
    <EventAddPopup
      :show="showEventAddPopup"
      :user-id="currentMisId"
      :persons-data="personsData"
      @close="handleCloseEventAddPopup"
      @save="handleSaveEventAdd"
    />

    <!-- 删除事件确认弹窗 -->
    <div v-if="showDeleteEventDialog" class="dialog-overlay">
      <div class="dialog-container">
        <div class="dialog-header">
          <div class="dialog-title">确认删除</div>
          <div class="dialog-close" @click="closeDeleteEventDialog">
            <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
          </div>
        </div>
        <div class="dialog-content">
          <div class="delete-warning">确定要删除这个事件吗？</div>
          <div class="delete-hint">删除后将无法恢复该事件的相关数据</div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="closeDeleteEventDialog">取消</button>
          <button
            class="delete-confirm-btn"
            :disabled="isDeletingEvent"
            @click="confirmDeleteEvent"
          >
            {{ isDeletingEvent ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 人物列表弹窗 -->
    <PersonList
      v-if="showPersonListPopup"
      :user-id="currentMisId"
      @close="closePersonListPopup"
      @refresh="handleRefreshRelationGraph"
    />

    <!-- 历史对话侧边栏 -->
    <HistorySidebar
      :is-open="showHistorySidebar"
      :current-title="''"
      @close="showHistorySidebar = false"
      @select-conversation="handleSelectConversation"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/free-mode';
import { showFailToast, showSuccessToast } from 'vant';
import Maintopbar from '@/components/Maintopbar.vue';
import EchartRelationGraph from '@/components/RelationshipGraph/EChartRelationGraph.vue';
import EventItem from '@/components/Chat/eventItem.vue';
import AddEventCard from '@/components/Chat/addEventCard.vue';
import PersonDetailPopup from '@/components/RelationshipGraph/PersonDetailPopup.vue';
import EventEditPopup from '@/components/Chat/eventEditPopup.vue';
import EventAddPopup from '@/components/Chat/eventAddPopup.vue';
import PersonList from '@/components/RelationshipGraph/personList.vue';
import HistorySidebar from '@/components/Chat/historySidebar.vue';
import {
  getRelationGraphData,
  getPersons,
  getUserProfile,
  type IGraphData,
  type IPersonData,
} from '@/apis/relation';
import { getUserInfo } from '@/apis/common';
import { getMemoryEvents, deletePersonEvent, type IEvent } from '@/apis/memory';
// 导入头像图片
import avatar1 from '@/assets/icon/laodong1.jpg';
import avatar2 from '@/assets/icon/laodong2.jpg';
import avatar3 from '@/assets/icon/laodong3.png';
import avatar4 from '@/assets/icon/laodong4.png';
import avatar5 from '@/assets/icon/laodong5.png';
import avatar6 from '@/assets/icon/laodong6.jpg';

const router = useRouter();

// Echart关系图数据接口
interface IEchartNode {
  id: string;
  label: string;
  originalId: string; // 保存原始ID用于边的映射
  type: 'core' | 'other';
  person_id?: string; // 添加person_id字段用于API调用
  avatar?: string; // 添加avatar字段用于显示头像
}

interface IEchartEdge {
  source: string;
  target: string;
  label: string;
}

interface IEchartRelationshipData {
  nodes: IEchartNode[];
  edges: IEchartEdge[];
}

// 图形尺寸
const graphWidth = ref(800);
const graphHeight = ref(600);

// 关系图数据
const graphData = ref<IGraphData | null>(null);
const echartGraphData = ref<IEchartRelationshipData | null>(null);
const personsData = ref<IPersonData[]>([]); // 保存原始的persons数据
const loading = ref(true);
const currentMisId = ref<string>('');

// 从index.vue迁移过来的变量
const isChatPlay = ref(false);
const showHistorySidebar = ref(false);
const userLoading = ref(false);

// 放大卡片相关状态
const showExpandedCard = ref(false);
const selectedEvent = ref<IEvent | null>(null);

// 事件编辑弹窗相关状态
const showEventEditPopup = ref(false);
const editingEvent = ref<IEvent | null>(null);

// 事件添加弹窗相关状态
const showEventAddPopup = ref(false);

// 人员详情弹窗相关状态
const showPersonDetailPopup = ref(false);
const selectedPersonId = ref<string>('');
const selectedPersonName = ref<string>('');
const selectedIsUserProfile = ref<boolean>(false);

// 人物列表弹窗相关状态
const showPersonListPopup = ref(false);

// 删除事件确认弹窗相关状态
const showDeleteEventDialog = ref(false);
const isDeletingEvent = ref(false);

// 事件数据
const events = ref<IEvent[]>([]);

// AI助理头像选择的存储键
const AI_ASSISTANT_STORAGE_KEY = 'selectedAssistantIndex';

// AI助手数据
const assistants = ref([
  {
    id: 1,
    name: '老董',
    avatar: avatar1,
  },
  {
    id: 2,
    name: '老董',
    avatar: avatar2,
  },
  {
    id: 3,
    name: '老董',
    avatar: avatar3,
  },
  {
    id: 4,
    name: '老董',
    avatar: avatar4,
  },
  {
    id: 5,
    name: '老董',
    avatar: avatar5,
  },
  {
    id: 6,
    name: '老董',
    avatar: avatar6,
  },
]);

// 立即从localStorage获取初始索引，避免跳变
const getInitialAssistantIndex = (): number => {
  const savedIndex = localStorage.getItem(AI_ASSISTANT_STORAGE_KEY);
  const index = savedIndex ? parseInt(savedIndex, 10) : 0;
  console.log('🔍 [relationGraph.vue] 获取初始助手索引:', {
    savedIndex,
    index,
  });
  if (index >= 0 && index < 6) {
    // 助手数组长度为6
    console.log('✅ [relationGraph.vue] 使用保存的助手索引:', index);
    return index;
  }
  console.log('⚠️ [relationGraph.vue] 使用默认助手索引: 0');
  return 0;
};

// 响应式的助手索引，立即初始化为正确值
const currentAssistantIndex = ref(getInitialAssistantIndex());

// 获取选中的助手头像
const selectedAssistantAvatar = computed(() => {
  const index = currentAssistantIndex.value;
  if (index >= 0 && index < assistants.value.length) {
    console.log(
      '✅ [relationGraph.vue] 计算属性返回助手头像:',
      assistants.value[index].name,
      assistants.value[index].avatar,
    );
    return assistants.value[index].avatar;
  }
  console.log(
    '⚠️ [relationGraph.vue] 计算属性返回默认助手头像:',
    assistants.value[0].name,
    assistants.value[0].avatar,
  );
  return assistants.value[0].avatar;
});

// 获取选中的助手名称
const selectedAssistantName = computed(() => {
  const index = currentAssistantIndex.value;
  if (index >= 0 && index < assistants.value.length) {
    return assistants.value[index].name;
  }
  return assistants.value[0].name;
});

// 计算属性：检查是否只有核心节点
const isOnlyCoreNode = computed(() => {
  if (!echartGraphData.value || !echartGraphData.value.nodes) {
    return false;
  }
  // 检查是否只有一个节点且为核心节点
  const realNodes = echartGraphData.value.nodes.filter(
    node => node.type === 'core' || node.type === 'other',
  );
  return realNodes.length === 1 && realNodes[0].type === 'core';
});

// 将relation.ts的IGraphData转换为Echart关系图数据格式
const transformToEchartData = (
  relationData: IGraphData,
  coreUserId: string,
  personsDataParam?: IPersonData[],
  userProfile?: IPersonData,
): IEchartRelationshipData => {
  const nodes: IEchartNode[] = [];
  const edges: IEchartEdge[] = [];

  console.log('🔄 [relationGraph.vue] transformToEchartData 开始转换数据:', {
    relationDataNodes: relationData.nodes.length,
    relationDataLinks: relationData.links.length,
    coreUserId,
    personsDataLength: personsDataParam?.length || 0,
    userProfileCanonicalName: userProfile?.canonical_name,
  });

  // 创建canonical_name到person_id和avatar的映射
  const nameToPersonIdMap = new Map<string, string>();
  const nameToAvatarMap = new Map<string, string>();

  // 如果有用户档案信息，先添加核心用户的映射
  if (userProfile) {
    // 使用canonical_name作为键
    nameToPersonIdMap.set(userProfile.canonical_name, userProfile.person_id);
    if (userProfile.avatar) {
      nameToAvatarMap.set(userProfile.canonical_name, userProfile.avatar);
    }

    // 同时使用coreUserId作为键，确保能够正确查找到
    nameToPersonIdMap.set(coreUserId, userProfile.person_id);
    if (userProfile.avatar) {
      nameToAvatarMap.set(coreUserId, userProfile.avatar);
    }

    console.log('📋 [relationGraph.vue] 添加核心用户映射:', {
      canonical_name: userProfile.canonical_name,
      coreUserId,
      person_id: userProfile.person_id,
      avatar: userProfile.avatar,
      mappingKeys: [userProfile.canonical_name, coreUserId],
    });
  }

  if (personsDataParam) {
    personsDataParam.forEach(person => {
      nameToPersonIdMap.set(person.canonical_name, person.person_id);
      if (person.avatar) {
        nameToAvatarMap.set(person.canonical_name, person.avatar);
      }
    });
    console.log(
      '📋 [relationGraph.vue] canonical_name到person_id映射:',
      Array.from(nameToPersonIdMap.entries()),
    );
    console.log(
      '📋 [relationGraph.vue] canonical_name到avatar映射:',
      Array.from(nameToAvatarMap.entries()),
    );
  } else {
    console.warn('⚠️ [relationGraph.vue] personsDataParam为空，无法创建映射');
  }

  // 用于处理重复节点ID的Map，记录每个原始ID出现的次数
  const idCountMap = new Map<string, number>();
  const duplicateNodeIds = new Set<string>();

  // 首先统计每个ID出现的次数
  relationData.nodes.forEach(node => {
    const count = idCountMap.get(node.id) || 0;
    idCountMap.set(node.id, count + 1);
    if (count > 0) {
      duplicateNodeIds.add(node.id);
    }
  });

  // 如果发现重复节点，记录详细信息
  if (duplicateNodeIds.size > 0) {
    console.warn(
      '⚠️ [relationGraph.vue] 检测到重复的节点ID，将为重复节点添加唯一后缀:',
      Array.from(duplicateNodeIds),
    );
    console.log('📊 [relationGraph.vue] 重复ID统计:', Object.fromEntries(idCountMap.entries()));
  }

  // 用于跟踪每个原始ID已经使用的次数
  const usedCountMap = new Map<string, number>();

  // 转换节点数据
  relationData.nodes.forEach(node => {
    // 为重复的节点ID生成唯一的ID
    let uniqueId = node.id;
    const originalId = node.id;
    const usedCount = usedCountMap.get(originalId) || 0;

    if (usedCount > 0) {
      // 为重复节点添加后缀，从第二个开始
      uniqueId = `${originalId}_${usedCount}`;
      console.log(`🔄 [relationGraph.vue] 为重复节点生成唯一ID: ${originalId} -> ${uniqueId}`);
    }

    usedCountMap.set(originalId, usedCount + 1);

    const isCore = originalId === coreUserId; // 使用原始ID判断是否为核心用户
    const personId = nameToPersonIdMap.get(originalId); // 使用原始ID查找person_id
    const avatar = nameToAvatarMap.get(originalId); // 使用原始ID查找avatar

    console.log('🔄 [relationGraph.vue] 处理节点:', {
      originalId,
      uniqueId,
      isCore,
      personId,
      hasPersonId: !!personId,
      avatar,
      coreUserId,
      avatarMapKeys: Array.from(nameToAvatarMap.keys()),
      avatarMapHasOriginalId: nameToAvatarMap.has(originalId),
    });

    // 对于核心节点，优先使用用户档案中的canonical_name，否则使用originalId
    let displayLabel = originalId;
    if (isCore && userProfile && userProfile.canonical_name) {
      displayLabel = userProfile.canonical_name;
      console.log('🔄 [relationGraph.vue] 核心节点使用canonical_name作为显示名称:', displayLabel);
    }

    nodes.push({
      id: uniqueId, // 使用唯一ID
      label: displayLabel, // 核心节点显示canonical_name，其他节点显示原始ID
      originalId, // 保存原始ID用于边的映射
      type: isCore ? 'core' : 'other',
      person_id: personId, // 添加person_id信息
      avatar, // 添加avatar信息
    });
  });

  // 创建原始ID到唯一ID的映射
  const originalIdToUniqueIdMap = new Map<string, string[]>();
  nodes.forEach(node => {
    const { originalId } = node; // 使用保存的原始ID
    if (!originalIdToUniqueIdMap.has(originalId)) {
      originalIdToUniqueIdMap.set(originalId, []);
    }
    originalIdToUniqueIdMap.get(originalId)!.push(node.id);
  });

  // 用于检测重复边
  const seenEdges = new Set<string>();

  // 转换边数据
  relationData.links.forEach(link => {
    // 获取源节点和目标节点的所有唯一ID
    const sourceUniqueIds = originalIdToUniqueIdMap.get(link.source) || [];
    const targetUniqueIds = originalIdToUniqueIdMap.get(link.target) || [];

    if (sourceUniqueIds.length === 0 || targetUniqueIds.length === 0) {
      console.warn('⚠️ [relationGraph.vue] 边引用了不存在的节点:', {
        source: link.source,
        target: link.target,
        sourceExists: sourceUniqueIds.length > 0,
        targetExists: targetUniqueIds.length > 0,
      });
      return;
    }

    // 为每个源节点和目标节点的组合创建边
    sourceUniqueIds.forEach(sourceId => {
      targetUniqueIds.forEach(targetId => {
        // 创建边的唯一标识符（双向边视为同一条边）
        const edgeKey = [sourceId, targetId].sort().join('-');
        if (seenEdges.has(edgeKey)) {
          return; // 跳过重复的边
        }
        seenEdges.add(edgeKey);

        edges.push({
          source: sourceId,
          target: targetId,
          label: '关系', // 简化关系标签
        });
      });
    });
  });

  console.log('✅ [relationGraph.vue] 数据转换完成:', {
    原始节点数量: relationData.nodes.length,
    原始边数量: relationData.links.length,
    有效节点数量: nodes.length,
    有效边数量: edges.length,
    重复节点数量: duplicateNodeIds.size,
    重复节点ID: Array.from(duplicateNodeIds),
    nodesWithPersonId: nodes.filter(n => n.person_id).length,
    nodesWithoutPersonId: nodes.filter(n => !n.person_id).length,
    节点ID映射: Object.fromEntries(originalIdToUniqueIdMap.entries()),
  });

  return {
    nodes,
    edges,
  };
};

// 获取事件记忆数据
const loadMemoryEvents = async (userId: string) => {
  try {
    console.log('🔄 [relationGraph.vue] 开始获取事件记忆数据...');
    const memoryData = await getMemoryEvents(userId, 100);
    console.log('📡 [relationGraph.vue] 事件记忆数据:', memoryData);

    if (memoryData && memoryData.result === 'success' && memoryData.events) {
      // 定义可能包含memory_type的项目类型
      interface IMemoryItem extends IEvent {
        memory_type?: string;
        canonical_name?: string;
        person_id?: string;
      }

      // 过滤掉person_profile类型的内容，只保留具体的事件
      const filteredEvents = memoryData.events.filter((item): item is IEvent => {
        const memoryItem = item as IMemoryItem;
        // 如果有memory_type字段且值为person_profile，则过滤掉
        if (memoryItem.memory_type === 'person_profile') {
          console.log(
            '🔄 [relationGraph.vue] 过滤掉person_profile:',
            memoryItem.canonical_name || memoryItem.person_id,
          );
          return false;
        }
        // 保留其他类型的内容（具体事件）
        return true;
      });

      events.value = filteredEvents;
      console.log(
        '✅ [relationGraph.vue] 事件记忆数据加载成功，原始数据',
        memoryData.events.length,
        '条，过滤后',
        events.value.length,
        '条事件',
      );
    } else {
      console.warn('⚠️ [relationGraph.vue] 事件记忆数据格式异常');
      events.value = [];
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 获取事件记忆数据失败:', error);
    // 如果API调用失败，不显示错误提示，只是保持空数组
    events.value = [];
  }
};

// 获取用户信息和关系图数据
const loadRelationData = async () => {
  try {
    loading.value = true;
    console.log('🔄 [relationGraph.vue] 开始获取用户信息...');

    // 获取用户信息
    const userInfo = await getUserInfo();
    console.log('📡 [relationGraph.vue] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentMisId.value = userInfo.login;
      console.log('✅ [relationGraph.vue] 用户信息加载成功, misId:', currentMisId.value);
    } else {
      console.warn('⚠️ [relationGraph.vue] 用户信息格式异常');
      currentMisId.value = 'unknown_user';
    }

    // 获取关系图数据
    console.log('🔄 [relationGraph.vue] 开始获取关系图数据...');

    // 先获取原始的persons数据
    const personsResponse = await getPersons({
      userId: currentMisId.value,
      limit: 100,
      offset: 0,
    });

    if (personsResponse.result === 'success' && personsResponse.persons) {
      personsData.value = personsResponse.persons;
      console.log('✅ [relationGraph.vue] 原始persons数据加载成功:', personsData.value);
    }

    // 获取用户档案信息（包含核心用户的头像）
    let userProfile: IPersonData | undefined;
    try {
      const userProfileResponse = await getUserProfile({
        user_id: currentMisId.value,
      });
      if (userProfileResponse.result === 'success' && userProfileResponse.person) {
        userProfile = userProfileResponse.person;
        console.log('✅ [relationGraph.vue] 用户档案加载成功:', userProfile);
      }
    } catch (error) {
      console.warn('⚠️ [relationGraph.vue] 获取用户档案失败:', error);
    }

    const relationData = await getRelationGraphData(currentMisId.value);
    console.log('📡 [relationGraph.vue] 关系图数据:', relationData);

    graphData.value = relationData;

    // 转换为Echart关系图数据格式，传入原始persons数据和用户档案
    echartGraphData.value = transformToEchartData(
      relationData,
      currentMisId.value,
      personsData.value,
      userProfile,
    );
    console.log('✅ [relationGraph.vue] 关系图数据加载成功，Echart数据:', echartGraphData.value);

    // 获取事件记忆数据
    await loadMemoryEvents(currentMisId.value);
  } catch (error) {
    console.error('❌ [relationGraph.vue] 获取关系图数据失败:', error);
    showFailToast('获取关系图数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 创建 person_id 到人名的映射
const personIdToNameMap = computed(() => {
  const map = new Map<string, string>();
  if (personsData.value) {
    personsData.value.forEach(person => {
      map.set(person.person_id, person.canonical_name);
    });
  }
  return map;
});

// 将 person_id 转换为人名显示
const getPersonNameById = (personId: string): string => {
  return personIdToNameMap.value.get(personId) || personId;
};

// 将参与者列表转换为人名显示
const getDisplayParticipants = (participants: string[]): string[] => {
  return participants.map(personId => getPersonNameById(personId));
};

// 获取放大卡片的情感样式类
const getExpandedSentimentClass = (sentiment: string) => {
  switch (sentiment) {
    case '积极':
    case '愉快':
    case '兴奋':
    case 'positive':
      return 'sentiment-positive';
    case '担忧':
    case '消极':
    case '悲伤':
    case 'negative':
      return 'sentiment-negative';
    case '中性':
    case 'neutral':
    default:
      return 'sentiment-neutral';
  }
};

// 获取情感显示文本
const getSentimentDisplayText = (sentiment: string) => {
  switch (sentiment) {
    case 'neutral':
      return '中性';
    case 'positive':
      return '积极';
    case 'negative':
      return '消极';
    default:
      return sentiment; // 如果已经是中文或其他值，直接返回
  }
};

// 处理事件卡片点击
const handleEventItemClick = (event: IEvent) => {
  selectedEvent.value = event;
  showExpandedCard.value = true;
};

// 处理修改事件按钮点击
const handleEditEvent = () => {
  if (selectedEvent.value) {
    editingEvent.value = { ...selectedEvent.value }; // 创建副本用于编辑
    showEventEditPopup.value = true;
  }
};

// 处理事件编辑弹窗关闭
const handleCloseEventEditPopup = () => {
  showEventEditPopup.value = false;
  editingEvent.value = null;
};

// 处理添加事件卡片点击
const handleAddEventClick = () => {
  showEventAddPopup.value = true;
};

// 处理事件添加弹窗关闭
const handleCloseEventAddPopup = () => {
  showEventAddPopup.value = false;
};

// 处理事件添加保存
const handleSaveEventAdd = async () => {
  // 关闭弹窗
  showEventAddPopup.value = false;

  // 重新加载事件数据
  try {
    await loadMemoryEvents(currentMisId.value);
    console.log('✅ [relationGraph.vue] 事件添加成功，已刷新事件列表');
  } catch (error) {
    console.error('❌ [relationGraph.vue] 刷新事件列表失败:', error);
  }
};

// 处理事件编辑保存
const handleSaveEventEdit = (updatedEvent: IEvent) => {
  console.log('📡 [relationGraph.vue] 事件编辑保存成功，更新本地数据:', updatedEvent);

  // 更新本地数据
  if (selectedEvent.value && selectedEvent.value.event_id === updatedEvent.event_id) {
    selectedEvent.value = { ...updatedEvent };
  }

  // 更新事件列表中的数据
  const eventIndex = events.value.findIndex(e => e.event_id === updatedEvent.event_id);
  if (eventIndex !== -1) {
    events.value[eventIndex] = { ...updatedEvent };
  }

  // 关闭编辑弹窗
  handleCloseEventEditPopup();
};

// 处理事件编辑更新（不关闭弹窗）
const handleUpdateEventEdit = (updatedEvent: IEvent) => {
  console.log('📡 [relationGraph.vue] 事件编辑更新成功，更新本地数据:', updatedEvent);

  // 更新本地数据
  if (selectedEvent.value && selectedEvent.value.event_id === updatedEvent.event_id) {
    selectedEvent.value = { ...updatedEvent };
  }

  // 更新事件列表中的数据
  const eventIndex = events.value.findIndex(e => e.event_id === updatedEvent.event_id);
  if (eventIndex !== -1) {
    events.value[eventIndex] = { ...updatedEvent };
  }

  // 注意：不关闭编辑弹窗，让用户可以继续编辑
};

// 处理删除事件按钮点击
const handleDeleteEvent = () => {
  if (selectedEvent.value) {
    console.log('🔄 [relationGraph.vue] 显示删除事件确认弹窗');
    showDeleteEventDialog.value = true;
  }
};

// 关闭删除事件确认弹窗
const closeDeleteEventDialog = () => {
  showDeleteEventDialog.value = false;
};

// 确认删除事件
const confirmDeleteEvent = async () => {
  if (!selectedEvent.value) {
    console.warn('⚠️ [relationGraph.vue] 没有选中的事件');
    return;
  }

  const eventToDelete = selectedEvent.value;

  try {
    isDeletingEvent.value = true;
    console.log('🔄 [relationGraph.vue] 开始删除事件...', {
      userId: currentMisId.value,
      eventId: eventToDelete.event_id,
    });

    // 立即从界面中移除事件卡片
    events.value = events.value.filter(event => event.event_id !== eventToDelete.event_id);

    // 关闭确认弹窗
    closeDeleteEventDialog();

    // 关闭放大卡片
    showExpandedCard.value = false;
    selectedEvent.value = null;

    // 显示删除成功提示
    showSuccessToast('删除成功');

    // 后台调用删除API
    const response = await deletePersonEvent({
      user_id: currentMisId.value,
      event_id: eventToDelete.event_id,
    });

    console.log('📡 [relationGraph.vue] 删除事件响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [relationGraph.vue] 事件删除API调用成功');
    } else {
      console.warn('⚠️ [relationGraph.vue] 删除事件API调用失败:', response);
      // 如果API调用失败，恢复事件到列表中
      events.value.push(eventToDelete);
      // 按时间戳重新排序
      events.value.sort(
        (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      );
      showFailToast(response?.reason || '删除事件失败，已恢复');
    }
  } catch (error) {
    console.error('❌ [relationGraph.vue] 删除事件失败:', error);
    // 如果API调用失败，恢复事件到列表中
    events.value.push(eventToDelete);
    // 按时间戳重新排序
    events.value.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    showFailToast('删除事件失败，已恢复');
  } finally {
    isDeletingEvent.value = false;
  }
};

// 处理核心节点按钮点击事件
const handleCoreNodeClick = () => {
  console.log('🔄 [relationGraph.vue] 核心节点按钮点击，显示人物列表弹窗');
  // 显示人物列表弹窗，让用户管理人际关系
  showPersonListPopup.value = true;
};

// 处理节点点击事件
const handleNodeClick = (nodeData: { id: string; person_id?: string }) => {
  console.log('🔄 [relationGraph.vue] 节点点击事件:', nodeData);
  console.log('🔄 [relationGraph.vue] 当前用户ID:', currentMisId.value);
  console.log('🔄 [relationGraph.vue] 节点详细信息:', {
    nodeId: nodeData.id,
    personId: nodeData.person_id,
    hasPersonId: !!nodeData.person_id,
    personIdType: typeof nodeData.person_id,
  });

  // 检查是否有person_id
  if (!nodeData.person_id) {
    console.warn('⚠️ [relationGraph.vue] 节点缺少person_id信息');
    console.warn('⚠️ [relationGraph.vue] 可用的节点数据:', echartGraphData.value?.nodes);
    return;
  }

  // 处理核心节点的点击 - 显示用户详情弹窗
  if (nodeData.id === currentMisId.value) {
    console.log('✅ [relationGraph.vue] 点击核心节点，显示用户详情弹窗');
    // 使用从API返回的真实person_id，而不是硬编码的'user_profile'
    selectedPersonId.value = nodeData.person_id;
    selectedPersonName.value = nodeData.id; // 使用用户ID作为显示名称
    selectedIsUserProfile.value = true; // 标记为用户档案
    showPersonDetailPopup.value = true;
    console.log('✅ [relationGraph.vue] 核心节点使用真实person_id:', nodeData.person_id);
    return;
  }

  // 处理非核心节点的点击 - 显示人员详情弹窗
  // 设置弹窗数据并显示
  selectedPersonId.value = nodeData.person_id;
  selectedPersonName.value = nodeData.id; // 使用canonical_name作为显示名称
  selectedIsUserProfile.value = false; // 标记为非用户档案
  showPersonDetailPopup.value = true;

  console.log('✅ [relationGraph.vue] 显示人员详情弹窗:', {
    currentMisId: currentMisId.value,
    personId: selectedPersonId.value,
    personName: selectedPersonName.value,
    personIdType: typeof selectedPersonId.value,
    personIdLength: selectedPersonId.value?.length,
  });
};

// 关闭人员详情弹窗
const closePersonDetailPopup = () => {
  showPersonDetailPopup.value = false;
  selectedPersonId.value = '';
  selectedPersonName.value = '';
  selectedIsUserProfile.value = false;
};

// 关闭人物列表弹窗
const closePersonListPopup = () => {
  showPersonListPopup.value = false;
};

// 刷新关系图数据
const handleRefreshRelationGraph = async () => {
  try {
    console.log('🔄 [relationGraph.vue] 刷新关系图数据...');

    // 设置加载状态，避免UI闪烁
    loading.value = true;

    // 重新获取关系图数据
    const relationData = await getRelationGraphData(currentMisId.value);
    console.log('📡 [relationGraph.vue] 刷新后的关系图数据:', relationData);

    graphData.value = relationData;

    // 重新获取原始的persons数据
    const personsResponse = await getPersons({
      userId: currentMisId.value,
      limit: 100,
      offset: 0,
    });

    if (personsResponse.result === 'success' && personsResponse.persons) {
      personsData.value = personsResponse.persons;
      console.log('✅ [relationGraph.vue] 刷新后的persons数据:', personsData.value);
    }

    // 重新获取用户档案信息（包含核心用户的头像）
    let userProfile: IPersonData | undefined;
    try {
      const userProfileResponse = await getUserProfile({
        user_id: currentMisId.value,
      });
      if (userProfileResponse.result === 'success' && userProfileResponse.person) {
        userProfile = userProfileResponse.person;
        console.log('✅ [relationGraph.vue] 刷新后的用户档案:', userProfile);
      }
    } catch (error) {
      console.warn('⚠️ [relationGraph.vue] 刷新时获取用户档案失败:', error);
    }

    // 转换为Echart关系图数据格式
    echartGraphData.value = transformToEchartData(
      relationData,
      currentMisId.value,
      personsData.value,
      userProfile,
    );
    console.log(
      '✅ [relationGraph.vue] 关系图数据刷新成功，新的节点数量:',
      echartGraphData.value?.nodes?.length || 0,
    );
  } catch (error) {
    console.error('❌ [relationGraph.vue] 刷新关系图数据失败:', error);
    showFailToast('刷新关系图数据失败');
  } finally {
    // 确保加载状态被重置
    loading.value = false;
  }
};

// 关闭放大的卡片
const handleCloseExpandedCard = () => {
  showExpandedCard.value = false;
  selectedEvent.value = null;
};

// 响应式调整图形尺寸
const updateGraphSize = () => {
  const container = document.querySelector('.graph-container');
  if (container) {
    graphWidth.value = container.clientWidth;
    graphHeight.value = container.clientHeight;
  }
};

// 从index.vue迁移过来的函数
// 处理添加新对话按钮点击
const handleAddChat = async () => {
  // 在关系图页面点击添加新对话按钮，跳转到chat.vue页面
  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' }, // 使用固定的'new'参数，chat.vue会生成实际的conversationId
  });
};

// 切换历史侧边栏显示状态
const toggleHistorySidebar = () => {
  showHistorySidebar.value = !showHistorySidebar.value;
};

// 处理人员列表按钮点击
const handlePersonListClick = () => {
  console.log('🔄 [relationGraph.vue] 人员列表按钮点击，显示人物列表弹窗');
  // 显示人物列表弹窗，与点击核心节点效果一致
  showPersonListPopup.value = true;
};

// 处理语音播放切换
const handleChatPlay = () => {
  isChatPlay.value = !isChatPlay.value;
  console.log('语音播放状态切换:', isChatPlay.value);
};

// 处理首页按钮点击
const handleHome = async () => {
  console.log('🏠 [relationGraph.vue] 点击home按钮，跳转到index页面聊天界面');
  // 清理sessionStorage数据
  sessionStorage.removeItem('returnToPersonDetail');
  sessionStorage.removeItem('relationGraphSource');
  // 设置从home按钮跳转的标记，让index页面直接显示聊天界面
  sessionStorage.setItem('fromHomeButton', 'true');
  await router.push({
    name: 'chat', // 跳转到index页面
  });
};

// 处理关系拓扑按钮点击（在关系图页面中，这个函数可能不需要，但为了保持一致性保留）
const handleRelationship = () => {
  console.log('已在关系拓扑页面');
  // 在关系图页面点击关系拓扑按钮，可以不做任何操作或者刷新数据
};

// 处理会话选择
const handleSelectConversation = (data: unknown) => {
  // 在关系图页面选择历史会话时，跳转到聊天页面并传递会话数据
  showHistorySidebar.value = false;

  // 检查数据格式
  if (data && typeof data === 'object' && 'conversationId' in data && 'title' in data) {
    const conversationData = data as { conversationId: string; title: string };

    // 将会话数据临时存储到sessionStorage，避免URL参数过长
    sessionStorage.setItem('pendingConversationRestore', JSON.stringify(data));

    // 跳转到聊天页面，使用会话标题作为路由参数
    void router.push({
      name: 'chat-conversation',
      params: { title: conversationData.title },
    });
  } else {
    console.error('❌ [relationGraph.vue] 会话数据格式错误:', data);
  }
};

// 用户头像相关函数
const getRandomColor = (misId: string) => {
  // 简单的颜色生成逻辑
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
  const index = misId.length % colors.length;
  return colors[index];
};

const getAvatarLetter = (misId: string) => {
  // 返回用户ID的第一个字符作为头像字母
  return misId ? misId.charAt(0).toUpperCase() : 'U';
};

// 处理开始聊天按钮点击
const handleStartChat = async () => {
  console.log('🔄 [relationGraph.vue] 开始聊天按钮点击，跳转到聊天页面');
  await router.push({
    name: 'chat-conversation',
    params: { title: 'new' },
  });
};

const handleAvatarClick = () => {
  console.log('用户头像点击');
  // 可以在这里添加用户头像点击的逻辑，比如显示用户信息弹窗
};

onMounted(async () => {
  updateGraphSize();
  window.addEventListener('resize', updateGraphSize);

  // 先加载关系数据，确保currentMisId已设置
  await loadRelationData();
});

onUnmounted(() => {
  window.removeEventListener('resize', updateGraphSize);
});
</script>

<style lang="scss" scoped>
// 统一配色方案
:root {
  --primary-color: #00bcd4;
  --primary-color-light: rgba(0, 188, 212, 0.1);
  --primary-color-medium: rgba(0, 188, 212, 0.2);
  --primary-color-strong: rgba(0, 188, 212, 0.3);

  --accent-color: #00ffff;
  --accent-color-light: rgba(0, 255, 255, 0.1);
  --accent-color-medium: rgba(0, 255, 255, 0.2);
  --accent-color-strong: rgba(0, 255, 255, 0.3);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-disabled: rgba(255, 255, 255, 0.5);

  --bg-glass: rgba(30, 58, 138, 0.15);
  --bg-glass-hover: rgba(30, 58, 138, 0.25);
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(0, 255, 255, 0.3);

  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.3);
  --shadow-accent: 0 0 20px rgba(0, 255, 255, 0.2);
}

// 容器样式
.relation-graph-container {
  background: url('@/assets/img/galaxy5.jpg') center center / cover no-repeat;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative; // 为星空粒子提供定位基准
}

.relation-graph-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative; // 相对定位
  z-index: 10; // 确保内容在星空粒子之上

  .header {
    width: 100%;
    flex-shrink: 0; // 防止头部被压缩
  }
}

.graph-container {
  flex: 1;
  width: 100%;
  min-height: 0; // 允许flex子项收缩
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 20px; // 与底部事件容器的间距
  background: transparent;
}

.graph-content {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.loading-text,
.error-text {
  color: var(--text-primary);
  font-size: 16px;
  text-align: center;
  padding: 20px;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

// 欢迎界面容器
.welcome-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 800px;
  z-index: 10;
}

.welcome-content {
  background: var(--bg-glass);
  border: 2px solid var(--border-accent);
  border-radius: 24px;
  padding: 40px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
}

.welcome-header {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-icon {
  margin-bottom: 24px;

  .assistant-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--accent-color-medium);
    box-shadow: var(--shadow-accent);
  }
}

// 标题样式现在使用全局设计系统中的 .cyber-title 和 .cyber-subtitle

.welcome-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

// 功能卡片样式现在使用全局设计系统中的类
.feature-text {
  flex: 1;
}

.welcome-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

// 按钮样式现在使用全局设计系统中的 .cyber-btn-primary 和 .cyber-btn-secondary
.cyber-btn-primary,
.cyber-btn-secondary {
  flex: 1;
  max-width: 200px;

  .btn-icon {
    font-size: 20px;
  }
}

.event-container {
  width: 100%;
  height: 310px;
  background: transparent;
  border-top: 1px solid var(--border-glass); // 顶部边框
  display: flex;
  align-items: center;
  padding: 0 20px;
  flex-shrink: 0; // 防止事件容器被压缩
  transition: all 0.3s ease;

  // 紧凑模式 - 空状态下缩小事件区域
  &.compact {
    height: 100px;
    border-top: 1px solid var(--text-disabled);
    opacity: 0.7;
  }
}

.event-swiper-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background: transparent;
}

.event-swiper {
  width: 100%;
  height: 250px;
  padding: 15px 0;
}

.event-slide {
  width: 220px !important;
  height: 220px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// 空状态容器样式
.empty-events-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
}

.empty-events-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  text-align: center;
}

.empty-events-text {
  color: var(--text-tertiary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: 0.3px;
  margin-bottom: 16px;
}

.empty-add-card {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 放大卡片遮罩层样式 - 统一为PersonDetailPopup风格
.expanded-card-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.expanded-card-container {
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.expanded-card {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.expanded-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;
  height: 80px;
}

.expanded-card-header .expanded-event-time {
  flex: 1;
}

.expanded-card-header .expanded-event-sentiment {
  margin-right: 12px;
}

.expanded-event-time {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26px;
  font-weight: 600;
  flex: 1;
}

.expanded-event-sentiment {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 22px;
  font-weight: 500;
  background: rgba(0, 188, 212, 0.1);
  color: #00bcd4;
  border: 2px solid #00bcd4;

  &.sentiment-positive {
    background: rgba(0, 188, 212, 0.1);
    color: #00bcd4;
    border-color: #00bcd4;
  }

  &.sentiment-negative {
    background: rgba(0, 188, 212, 0.1);
    color: #00bcd4;
    border-color: #00bcd4;
  }

  &.sentiment-neutral {
    background: rgba(0, 188, 212, 0.1);
    color: #00bcd4;
    border-color: #00bcd4;
  }
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
  }

  .close-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
  }
}

.expanded-card-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.expanded-event-description {
  border: none;
  border-radius: 16px;
  padding: 20px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.9);
  font-size: 24px;
  line-height: 1.6;
  margin: 0;
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
}

.expanded-event-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.expanded-detail-item {
  border: none;
  border-radius: 16px;
  padding: 20px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.expanded-detail-label {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin-right: 8px;
}

.expanded-detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.expanded-detail-value {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24px;
  line-height: 1.6;

  &.tag-style {
    color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid #00ffff;
    border-radius: 16px;
    padding: 6px 12px;
    font-size: 20px;
    display: inline-block;
    white-space: nowrap;
  }
}

// 底部按钮区域样式
.expanded-card-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn {
  flex: 1;
  padding: 16px 16px;
  border-radius: 20px;
  font-size: 28px;
  font-weight: 600;
  cursor: pointer;
  border: 2px solid;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 200px;

  &.delete-btn {
    color: #ef4444;
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
  }

  &.edit-btn {
    color: #00bcd4;
    border-color: #00bcd4;
    background: rgba(0, 188, 212, 0.1);
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001; // 确保在放大卡片之上
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 500px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  height: 80px;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px;
    font-weight: 600;
    flex: 1;
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  .delete-warning {
    color: #ffffff;
    font-size: 32px;
    font-weight: 600;
    line-height: 1.4;
    margin: 0 0 12px 0;
    text-align: center;

    strong {
      color: #ef4444;
    }
  }

  .delete-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 24px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .cancel-btn,
  .confirm-btn,
  .delete-confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 28px;
    font-weight: 600;
    cursor: pointer;
    border: 2px solid;
    background: transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    width: 200px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }
  }

  .confirm-btn {
    color: #00bcd4;
    border-color: #00bcd4;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }
  }

  .delete-confirm-btn {
    color: #ef4444;
    border-color: #ef4444;

    &:hover:not(:disabled) {
      background: rgba(239, 68, 68, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }
  }
}
</style>
