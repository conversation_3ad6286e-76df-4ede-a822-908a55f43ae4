<template>
  <div class="deep-thinking-wrapper">
    <div class="deep-thinking-box" @click="handleDeepThinking">
      <div class="deep-thinking-status">
        {{ reasoningData.status === ReasoningStatus.PROCESSING ? '思考中...' : '已深度思考' }}
      </div>
      <van-icon v-if="expanded" name="arrow-up" class="expanded-icon" />
      <van-icon v-else name="arrow-down" class="expanded-icon" />
    </div>
    <div v-show="expanded" class="deep-thinking-content">
      <!-- 渲染分离后的思考步骤 -->
      <div
        v-for="(step, index) in parsedThinkingSteps"
        :key="index"
        class="thinking-step"
        style="white-space: pre-wrap; word-break: break-word"
      >
        {{ step }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRefs } from 'vue';
import { Icon as vanIcon } from 'vant';
import { ReasoningStatus } from '@/constants/chat';

interface IProps {
  reasoningData: IReasoningData;
}

const props = defineProps<IProps>();
const { reasoningData } = toRefs(props);
const expanded = ref(true);

// 解析思考步骤，将多个思考内容分离
const parsedThinkingSteps = computed(() => {
  const { content } = reasoningData.value;
  if (!content) return [];

  console.log('DeepThinking - 原始思考内容:', content);

  // 移除开头的换行符
  const cleanContent = content.startsWith('\n') ? content.replace(/^\n/, '') : content;

  // 去重处理：如果内容中有重复的[Reflection]块，只保留唯一的
  const steps: string[] = [];
  const seenSteps = new Set<string>();

  // 检查是否包含 [Reflection] 标记
  if (cleanContent.includes('[Reflection]')) {
    // 按 [Reflection] 分割，但保留原始格式
    const parts = cleanContent.split(/(?=\[Reflection\])/g);

    parts.forEach(part => {
      const trimmedPart = part.trim();
      if (trimmedPart && trimmedPart !== '[Reflection]') {
        // 去重：只添加未见过的步骤
        if (!seenSteps.has(trimmedPart)) {
          seenSteps.add(trimmedPart);
          steps.push(handleCitationFormat(trimmedPart));
        }
      }
    });
  } else {
    // 如果没有 [Reflection] 标记，尝试其他格式的分割
    // 使用正则表达式匹配 [任意内容]任意内容。 的格式
    const reflectionRegex = /\[([^[\]]*?)\]([^[]*?)(?=\[|$)/g;
    const matches = Array.from(cleanContent.matchAll(reflectionRegex));

    matches.forEach(match => {
      const reflectionType = match[1].trim();
      const stepContent = match[2].trim();
      if (stepContent) {
        const fullStep = `[${reflectionType}]${stepContent}`;
        // 去重：只添加未见过的步骤
        if (!seenSteps.has(fullStep)) {
          seenSteps.add(fullStep);
          steps.push(handleCitationFormat(fullStep));
        }
      }
    });
  }

  // 如果仍然没有步骤，返回原始内容作为单个步骤
  if (steps.length === 0 && cleanContent) {
    steps.push(handleCitationFormat(cleanContent));
  }

  console.log('DeepThinking - 解析后的步骤:', steps);
  return steps;
});

// 格式化引用: [citation:1] -> [1]
const handleCitationFormat = (content = '') => {
  return content.replace(/\[\s*citation:(\d+)\]/g, '[$1]');
};
const handleDeepThinking = () => {
  expanded.value = !expanded.value;
};
</script>

<style scoped lang="scss">
.deep-thinking-box {
  width: fit-content;
  display: flex;
  border-radius: 16px;
  justify-content: space-between;
  align-items: center;
  padding: 0px 16px;
  font-size: 24px;
  cursor: pointer;
  background: rgba(1, 28, 32, 0.6);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  margin-bottom: 20px;
  .deep-thinking-status {
    margin-right: 6px;
    padding: 0;
    color: #000;
    line-height: 48px;
  }
  .expanded-icon {
    font-size: 24px;
  }
}

.deep-thinking-content {
  color: #8b8b8b;
  font-size: 24px;
  border-left: 2px solid #e5e5e5;
  padding-left: 10px;
  margin-bottom: 20px;
  line-height: 48px;

  .thinking-step {
    margin-bottom: 16px;
    padding: 8px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
