<template>
  <div class="chat-message" :class="{ 'is-user': messageData.role === 'user' }">
    <!-- 调试信息 -->
    <div
      v-if="false"
      style="
        font-size: 12px;
        color: #999;
        background: rgba(0, 0, 0, 0.1);
        padding: 8px;
        margin: 4px 0;
        border-radius: 4px;
      "
    >
      <div>Role: {{ messageData.role }}</div>
      <div>isToolCallLoading: {{ messageData.isToolCallLoading }}</div>
      <div>currentToolName: {{ messageData.currentToolName }}</div>
      <div>
        shouldShowLoadingTip:
        {{ messageData.isToolCallLoading && messageData.role === 'assistant' }}
      </div>
    </div>
    <div>
      <LoadingTip
        v-if="messageData.isToolCallLoading && messageData.role === 'assistant'"
        :tool-name="messageData.currentToolName"
      />
    </div>
    <div class="message-container">
      <!-- 消息内容上方展示AI推理依据 -->
      <PretoolsCard
        v-if="
          messageData.role === 'assistant' && messageData.pre_tools && messageData.pre_tools.length
        "
        :pretools="messageData.pre_tools"
      />
      <div
        class="message-content"
        :class="{
          'loading-content': !messageData.isFinish && messageData.role === 'assistant',
        }"
      >
        <template
          v-if="messageData.isFinish || messageData.content || messageData.reasoningData.content"
        >
          <template v-if="messageData.role === 'assistant'">
            <DeepThinking
              v-if="messageData.reasoningData.content"
              :reasoning-data="messageData.reasoningData"
            ></DeepThinking>
            <messageRender v-if="messageData.content" :text="messageData.content"></messageRender>
          </template>
          <span v-else>{{ messageData.content }}</span>
        </template>
        <div v-else-if="!messageData.isFinish && messageData.role === 'assistant'" class="loading">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <!-- 操作按钮 (仅在 role=assistant 时显示) -->
        <div
          v-if="messageData.isFinish && messageData.role === 'assistant' && messageData.content"
          class="action-buttons"
        >
          <div class="action-btn" @click="handlePlay">
            <i
              v-if="
                !isCurrentAudioPlaying(messageData.debugInfo?.id || '') ||
                audioStatus === 'completed'
              "
              class="iconfont icon-roo-sqt-laba"
            ></i>
            <template v-else>
              <i v-if="audioStatus === 'loading'" class="iconfont icon-sg-loading"></i>
              <img v-else class="kaiqi-img" src="@/assets/img/langdu_kaiqi.png" alt="" />
            </template>
          </div>
          <div class="action-btn" @click="handleCopy">
            <i class="iconfont icon-mtdui-copy-o"></i>
          </div>
          <div v-if="isRegenerate" class="action-btn" @click="handleRegenerate">
            <i class="iconfont icon-sg-refresh"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, watch } from 'vue';
import messageRender from '@/components/Chat/messageRender.vue';
import { copyToClipboard } from '@/utils';
import DeepThinking from './DeepThinking.vue';
import LoadingTip from './LoadingTip.vue';
import PretoolsCard from './PretoolsCard.vue';
import { useAudioQueue } from '../useAudioPlayer';

const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();

interface IProps {
  messageData: IChatStreamContent;
  isRegenerate: boolean;
}

const props = defineProps<IProps>();
const emit = defineEmits(['regenerate']);

// 使用 watch 监听 messageData 的变化，在数据更新后输出日志
watch(
  () => props.messageData,
  newVal => {
    // 只关注工具调用状态变化
    if (newVal?.isToolCallLoading !== undefined) {
      console.log('🚀 TOOL_CALL_DEBUG: chatItem工具调用状态变化:', {
        isToolCallLoading: newVal.isToolCallLoading,
        currentToolName: newVal.currentToolName,
        shouldShowLoadingTip: newVal.isToolCallLoading && newVal.role === 'assistant',
      });

      // 特别关注LoadingTip的显示条件
      if (newVal.isToolCallLoading && newVal.role === 'assistant') {
        console.log('🚀 TOOL_CALL_DEBUG: chatItem应该显示LoadingTip组件');
      } else if (!newVal.isToolCallLoading && newVal.role === 'assistant') {
        console.log('🚀 TOOL_CALL_DEBUG: chatItem应该隐藏LoadingTip组件');
      }
    }
  },
  { deep: true },
);
const handlePlay = () => {
  console.log(11);
  if (isCurrentAudioPlaying(props.messageData.debugInfo?.id || '')) {
    stop();
  } else {
    play({
      id: props.messageData.debugInfo?.id || '',
      text: props.messageData.content,
      type: 'manualPlay',
    });
  }
};
const handleRegenerate = () => {
  emit('regenerate', props.messageData);
};
const handleCopy = () => {
  copyToClipboard(props.messageData.content);
};
</script>

<style scoped lang="scss">
.chat-message {
  width: 100%;
  margin: 24px 0px;
  box-sizing: border-box;
  position: relative;

  .message-container {
    display: flex;
    flex-direction: column;

    .message-content {
      width: fit-content;
      max-width: 80%;
      padding: 20px;
      background: var(--bg-glass);
      border: 2px solid var(--border-accent);
      border-radius: 16px 16px 16px 4px;
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-strong), var(--shadow-accent);

      // 使用设计系统字体
      color: var(--text-primary) !important;
      font-size: var(--font-size-3xl);
      font-weight: 400;
      line-height: 1.5;

      // 修复文本选中样式
      ::selection {
        background-color: var(--primary-color-medium);
        color: var(--text-primary);
      }

      ::-moz-selection {
        background-color: var(--primary-color-medium);
        color: var(--text-primary);
      }

      // 确保所有子元素都使用正确的文字颜色
      * {
        color: inherit !important;
      }

      // 特别处理可能的黑色文字
      p,
      span,
      div,
      text {
        color: var(--text-primary) !important;
      }

      &.loading-content {
        border-radius: 16px 16px 16px 0px;
      }

      .loading {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: flex-start;
        padding: 0;
        min-height: auto;

        .dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: var(--accent-color);
          opacity: 0.3;
          animation: dotFade 1.4s infinite;
          box-shadow: 0 0 8px var(--accent-color-medium);

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.2s;
          }

          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }
    }
  }

  &.is-user {
    margin-left: auto;

    .message-container {
      flex-direction: row-reverse;

      .message-content {
        font-weight: 500;
        color: var(--text-primary) !important;
        background: var(--primary-color-light);
        border: 2px solid var(--primary-color);
        border-radius: 16px 16px 4px 16px;
        box-shadow:
          var(--shadow-strong),
          0 0 20px var(--primary-color-strong);
      }
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 24px;
    color: var(--text-tertiary);
    padding-top: 16px;

    .action-btn {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-glass);
      border: 1px solid var(--border-glass);
      border-radius: 50%;
      font-size: 24px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
        box-shadow: var(--shadow-accent);
      }

      .icon-sg-loading {
        color: var(--accent-color);
        animation: loadingSpin 2s linear infinite;
      }

      .kaiqi-img {
        width: 24px;
        height: auto;
        filter: brightness(0) invert(1);
      }
    }
  }
}

@keyframes loadingSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dotFade {
  0%,
  80%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .chat-message {
    margin: 16px 0px;

    .message-container {
      .message-content {
        max-width: 90%;
        padding: 16px;
        border-radius: 12px 12px 12px 4px;
        font-size: var(--font-size-3xl) !important;
        line-height: 1.5;
        color: var(--text-primary) !important;

        &.loading-content {
          border-radius: 12px 12px 12px 0px;
        }
      }
    }

    &.is-user {
      .message-container {
        .message-content {
          border-radius: 12px 12px 4px 12px;
        }
      }
    }

    .action-buttons {
      gap: 16px;
      padding-top: 12px;

      .action-btn {
        width: 32px;
        height: 32px;
        font-size: 20px;

        .kaiqi-img {
          width: 20px;
        }
      }
    }
  }
}
</style>
